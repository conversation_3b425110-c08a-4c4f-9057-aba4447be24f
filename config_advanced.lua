-- ======================================
-- CB Killfeed - إعدادات متقدمة للمطورين
-- الإصدار: 2.0 المطور
-- ======================================

ConfigAdvanced = {}

-- ======================================
-- إعدادات الأداء والتحسين
-- ======================================

-- تحسينات الذاكرة
ConfigAdvanced.Performance = {
    -- الحد الأقصى لعدد القتلات المحفوظة في الذاكرة
    maxKillsInMemory = 100,
    
    -- تنظيف الذاكرة كل X ثانية
    memoryCleanupInterval = 300, -- 5 دقائق
    
    -- تأخير معالجة القتل (بالميلي ثانية) لتجنب الضغط
    killProcessingDelay = 50,
    
    -- تحسين الرسوم المتحركة
    animationOptimization = true,
    
    -- ضغط البيانات المحفوظة
    compressStoredData = true
}

-- ======================================
-- إعدادات الأمان والحماية
-- ======================================

ConfigAdvanced.Security = {
    -- تشفير الإعدادات المحفوظة
    encryptSettings = true,
    
    -- التحقق من صحة البيانات المستوردة
    validateImportedData = true,
    
    -- الحد الأقصى لحجم الملفات المرفوعة (بالبايت)
    maxFileSize = 5 * 1024 * 1024, -- 5MB
    
    -- أنواع الملفات المسموحة للصور
    allowedImageTypes = {"image/png", "image/jpeg", "image/gif"},
    
    -- أنواع الملفات المسموحة للأصوات
    allowedAudioTypes = {"audio/mp3", "audio/wav", "audio/ogg"},
    
    -- منع التلاعب بالإعدادات من الكونسول
    preventConsoleManipulation = true
}

-- ======================================
-- إعدادات التصدير والاستيراد
-- ======================================

ConfigAdvanced.ImportExport = {
    -- تفعيل النسخ الاحتياطية التلقائية
    autoBackupEnabled = true,
    
    -- فترة النسخ الاحتياطي التلقائي (بالميلي ثانية)
    autoBackupInterval = 600000, -- 10 دقائق
    
    -- الحد الأقصى لعدد النسخ الاحتياطية المحفوظة
    maxBackupFiles = 5,
    
    -- ضغط ملفات التصدير
    compressExports = true,
    
    -- تضمين الطابع الزمني في أسماء الملفات
    includeTimestamp = true
}

-- ======================================
-- إعدادات الإشعارات المتقدمة
-- ======================================

ConfigAdvanced.Notifications = {
    -- الحد الأقصى لعدد الإشعارات المعروضة في نفس الوقت
    maxConcurrentNotifications = 3,
    
    -- تأخير بين الإشعارات (بالميلي ثانية)
    notificationDelay = 500,
    
    -- تفعيل الأصوات للإشعارات
    notificationSounds = true,
    
    -- تفعيل الاهتزاز للإشعارات (إن كان مدعوماً)
    notificationVibration = false,
    
    -- إعدادات الإشعارات المخصصة
    customNotifications = {
        -- إشعار عند الوصول لـ 50 قتلة
        milestone50Kills = {
            enabled = true,
            title = "إنجاز مميز!",
            message = "وصلت إلى 50 قتلة!",
            icon = "🏆"
        },
        
        -- إشعار عند الوصول لـ 100 قتلة
        milestone100Kills = {
            enabled = true,
            title = "إنجاز رائع!",
            message = "وصلت إلى 100 قتلة!",
            icon = "👑"
        }
    }
}

-- ======================================
-- إعدادات الإحصائيات المتقدمة
-- ======================================

ConfigAdvanced.Statistics = {
    -- تتبع إحصائيات مفصلة لكل سلاح
    detailedWeaponStats = true,
    
    -- تتبع إحصائيات الوقت (ساعات اللعب)
    timeBasedStats = true,
    
    -- تتبع إحصائيات المسافة المفصلة
    distanceStats = true,
    
    -- حفظ تاريخ القتلات (آخر 100 قتلة)
    killHistory = true,
    
    -- تصدير الإحصائيات تلقائياً
    autoExportStats = false,
    
    -- فترة التصدير التلقائي (بالساعات)
    autoExportInterval = 24
}

-- ======================================
-- إعدادات التصفية المتقدمة
-- ======================================

ConfigAdvanced.Filtering = {
    -- تصفية متقدمة حسب الوقت
    timeBasedFiltering = true,
    
    -- تصفية حسب اللاعبين المحددين
    playerBasedFiltering = true,
    
    -- تصفية حسب نوع الخادم
    serverTypeFiltering = false,
    
    -- حفظ فلاتر مخصصة
    customFilters = true,
    
    -- الحد الأقصى لعدد الفلاتر المحفوظة
    maxSavedFilters = 10
}

-- ======================================
-- إعدادات التخصيص المتقدم
-- ======================================

ConfigAdvanced.Customization = {
    -- تفعيل الثيمات المخصصة
    customThemes = true,
    
    -- تفعيل الخطوط المخصصة
    customFonts = false,
    
    -- تفعيل التأثيرات البصرية المتقدمة
    advancedEffects = true,
    
    -- تفعيل الرسوم المتحركة المخصصة
    customAnimations = true,
    
    -- دعم CSS مخصص
    customCSS = false
}

-- ======================================
-- إعدادات التطوير والتصحيح
-- ======================================

ConfigAdvanced.Development = {
    -- تفعيل وضع التطوير
    debugMode = false,
    
    -- عرض رسائل التصحيح في الكونسول
    showDebugMessages = false,
    
    -- تسجيل الأحداث في ملف
    logEvents = false,
    
    -- مسار ملف السجل
    logFilePath = "logs/killfeed.log",
    
    -- تفعيل إحصائيات الأداء
    performanceStats = false
}

-- ======================================
-- إعدادات التكامل مع السكريبتات الأخرى
-- ======================================

ConfigAdvanced.Integration = {
    -- تكامل مع ESX
    ESXIntegration = false,
    
    -- تكامل مع QBCore
    QBCoreIntegration = false,
    
    -- تكامل مع vRP
    vRPIntegration = false,
    
    -- تكامل مع Discord
    DiscordIntegration = false,
    
    -- webhook للإحصائيات
    DiscordWebhook = "",
    
    -- تكامل مع قواعد البيانات
    DatabaseIntegration = false,
    
    -- نوع قاعدة البيانات (mysql, sqlite)
    DatabaseType = "mysql"
}

-- ======================================
-- إعدادات اللغة والترجمة
-- ======================================

ConfigAdvanced.Localization = {
    -- اللغة الافتراضية
    defaultLanguage = "ar",
    
    -- اللغات المدعومة
    supportedLanguages = {"ar", "en", "fr", "de"},
    
    -- تفعيل الترجمة التلقائية
    autoTranslation = false,
    
    -- مسار ملفات الترجمة
    translationPath = "locales/"
}

-- ======================================
-- إعدادات الشبكة والاتصال
-- ======================================

ConfigAdvanced.Network = {
    -- تأخير إرسال البيانات (بالميلي ثانية)
    networkDelay = 100,
    
    -- ضغط البيانات المرسلة
    compressNetworkData = true,
    
    -- التحقق من الاتصال
    connectionCheck = true,
    
    -- إعادة المحاولة عند فشل الإرسال
    retryOnFailure = true,
    
    -- عدد المحاولات الأقصى
    maxRetries = 3
}

-- ======================================
-- إعدادات التحديثات
-- ======================================

ConfigAdvanced.Updates = {
    -- تفعيل التحقق من التحديثات
    checkForUpdates = true,
    
    -- فترة التحقق من التحديثات (بالساعات)
    updateCheckInterval = 24,
    
    -- رابط التحقق من التحديثات
    updateCheckURL = "https://api.cbstore.com/killfeed/version",
    
    -- تنزيل التحديثات تلقائياً
    autoDownloadUpdates = false,
    
    -- إشعار عند توفر تحديث
    notifyOnUpdate = true
}

-- ======================================
-- إعدادات الخادم المتقدمة
-- ======================================

ConfigAdvanced.Server = {
    -- تفعيل إحصائيات الخادم
    serverStats = false,
    
    -- حفظ إحصائيات اللاعبين في قاعدة البيانات
    savePlayerStats = false,
    
    -- مشاركة الإحصائيات بين الخوادم
    crossServerStats = false,
    
    -- تفعيل الترتيب العالمي
    globalLeaderboard = false,
    
    -- فترة تحديث الترتيب (بالدقائق)
    leaderboardUpdateInterval = 60
}

-- ======================================
-- دوال المساعدة للمطورين
-- ======================================

-- دالة للحصول على إعداد متقدم
function GetAdvancedConfig(category, setting)
    if ConfigAdvanced[category] and ConfigAdvanced[category][setting] then
        return ConfigAdvanced[category][setting]
    end
    return nil
end

-- دالة لتعديل إعداد متقدم
function SetAdvancedConfig(category, setting, value)
    if ConfigAdvanced[category] then
        ConfigAdvanced[category][setting] = value
        return true
    end
    return false
end

-- دالة للتحقق من تفعيل ميزة
function IsFeatureEnabled(category, feature)
    local config = GetAdvancedConfig(category, feature)
    return config == true
end

-- ======================================
-- تصدير الإعدادات للاستخدام العام
-- ======================================

-- دمج الإعدادات المتقدمة مع الإعدادات الأساسية
if Config then
    Config.Advanced = ConfigAdvanced
else
    Config = {Advanced = ConfigAdvanced}
end
