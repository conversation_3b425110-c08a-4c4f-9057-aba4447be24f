/* Custom Font */

@font-face {
	font-family: Valo;
	src: url(valo.ttf);
}

#killfeed-container {
	margin-top: 17%;
	margin-right: 1vh;
	width: 49vw;
	height: 60vh;
	float: right;
	z-index: 9999;
	position: relative;
	overflow-y: hidden;
	overflow-x: hidden;
}

.kill-line {
	float: right;
	width: 100%;
	margin-bottom: 0.35vh;
	animation-fill-mode: forwards;
}

.kill-container {
	height: auto;
	min-height: 3.5vh;
	float: right;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 0.2vh 0;
}

.line-clamp {
	display: -webkit-box;
	-webkit-line-clamp: 1;
	line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.black-design {
	background-color: rgba(30, 30, 30, 0.288);
	border-radius: 0.5em;
	padding: 0.3em 0.3em 0.3em 0.3em;
	box-shadow: rgba(42, 42, 42, 0.692) 0px 4px 12px;
}

.teal-design {
	background-color: rgba(143, 16, 155, 0.295);
	border-radius: 0.5em;
	padding: 0.3em 0.3em 0.3em 0.3em;
	box-shadow: rgba(197, 30, 189, 0.664) 0px 4px 12px;
}

.red-design {
	background-color: rgba(190, 165, 25, 0.247);
	border-radius: 0.5em;
	padding: 0.3em 0.3em 0.3em 0.3em;
	box-shadow: rgba(190, 165, 25, 0.699) 0px 4px 12px;
}

.text {
	padding: 0;
	text-align: center;
	margin: 0;
	color: rgb(255, 255, 255);
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.name {
	padding-left: 0.85vh;
	padding-right: 0.85vh;
	font-size: 1.2em;
	font-weight: 500;
}

.tag {
	padding-left: 0.85vh;
	margin-right: -0.30vh;
	font-size: 1.20em;
}

.message {
	padding-right: 0.85vh;
	font-size: 1.35em;
}

.dist {
	padding-left: 0.5vh;
	padding-right: 0.5vh;
	padding-top: 0.3vh;
	padding-bottom: 0.3vh;
	margin-right: 0.6vh;
	margin-left: 0.3vh;
	font-size: 0.9em;
	white-space: nowrap;
	font-weight: 600;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
	border: solid rgba(255, 255, 255, 0.8) 1px;
	border-radius: 8px;
	backdrop-filter: blur(5px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.none {
	padding: 0;
	padding-right: 0.85vh;
}

.weapon-image {
	height: 100%;
	width: auto;
	max-height: 3.5vh;
	min-height: 2.3vh;
	max-width: 4vh;
	object-fit: contain;
	vertical-align: middle;
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
	transition: all 0.3s ease;
}

.weapon-image:hover {
	transform: scale(1.1);
	filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
}

.icon-image {
	height: 100%;
	width: auto;
	max-height: 3.8vh;
	min-height: 2.6vh;
	max-width: 3.5vh;
	object-fit: contain;
	vertical-align: middle;
	padding: 0;
	padding-left: 0.85vh;
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
	transition: all 0.3s ease;
}

.icon-image:hover {
	transform: scale(1.05);
	filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
}

/* ANIMATIONS  */

.animate__animated.animate__fadeInRight {
	--animate-duration: 300ms;
}
.animate__animated.animate__flipOutX {
	--animate-duration: 0.6s;
}

/* قائمة الإعدادات */
.settings-menu {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 40, 0.8));
	backdrop-filter: blur(15px);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10000;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	animation: fadeIn 0.3s ease-out;
	pointer-events: all;
}

.settings-menu.hidden {
	display: none;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		backdrop-filter: blur(0px);
	}
	to {
		opacity: 1;
		backdrop-filter: blur(15px);
	}
}

.settings-container {
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.98), rgba(50, 50, 70, 0.95));
	border-radius: 20px;
	padding: 25px;
	width: 90%;
	max-width: 650px;
	max-height: 85vh;
	overflow-y: auto;
	box-shadow:
		0 25px 50px rgba(0, 0, 0, 0.6),
		0 0 0 1px rgba(255, 255, 255, 0.1),
		inset 0 1px 0 rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.15);
	animation: slideIn 0.4s ease-out;
	position: relative;
}

@keyframes slideIn {
	from {
		transform: translateY(-50px) scale(0.9);
		opacity: 0;
	}
	to {
		transform: translateY(0) scale(1);
		opacity: 1;
	}
}

.settings-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg, transparent, rgba(143, 16, 155, 0.8), transparent);
	border-radius: 20px 20px 0 0;
}

.settings-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	padding-bottom: 15px;
	border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.welcome-message {
	background: linear-gradient(135deg, rgba(143, 16, 155, 0.1), rgba(143, 16, 155, 0.05));
	border: 1px solid rgba(143, 16, 155, 0.3);
	border-radius: 10px;
	padding: 15px;
	margin-bottom: 20px;
	color: #ffffff;
}

.welcome-message p {
	margin: 0 0 10px 0;
	font-size: 1.1em;
	font-weight: 500;
}

.welcome-message ul {
	margin: 0;
	padding-right: 20px;
	list-style-type: none;
}

.welcome-message li {
	margin-bottom: 5px;
	position: relative;
	padding-right: 15px;
}

.welcome-message li::before {
	content: '✓';
	position: absolute;
	right: -5px;
	color: #4CAF50;
	font-weight: bold;
}

.settings-header h2 {
	color: #ffffff;
	margin: 0;
	font-size: 2em;
	font-weight: 700;
	text-shadow: 0 2px 10px rgba(143, 16, 155, 0.5);
	background: linear-gradient(135deg, #ffffff, #e0e0e0);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.close-btn {
	background: rgba(255, 0, 0, 0.2);
	border: 1px solid rgba(255, 0, 0, 0.5);
	color: #ff4444;
	width: 35px;
	height: 35px;
	border-radius: 50%;
	font-size: 1.5em;
	cursor: pointer;
	transition: all 0.3s ease;
}

.close-btn:hover {
	background: rgba(255, 0, 0, 0.4);
	transform: scale(1.1);
}

.settings-section {
	margin-bottom: 25px;
	padding: 20px;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
	border-radius: 15px;
	border: 1px solid rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10px);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.settings-section::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
	transition: left 0.5s ease;
}

.settings-section:hover::before {
	left: 100%;
}

.settings-section:hover {
	border-color: rgba(143, 16, 155, 0.3);
	box-shadow: 0 5px 20px rgba(143, 16, 155, 0.2);
	transform: translateY(-2px);
}

.settings-section h3 {
	color: #ffffff;
	margin: 0 0 15px 0;
	font-size: 1.3em;
	font-weight: 500;
}

.color-options, .general-options, .image-options {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.color-option, .option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 10px;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.color-option label, .option label {
	color: #ffffff;
	font-size: 1.1em;
	font-weight: 400;
	flex: 1;
}

.color-option input[type="color"] {
	width: 50px;
	height: 35px;
	border: none;
	border-radius: 8px;
	cursor: pointer;
	margin-right: 10px;
}

.color-preview {
	width: 30px;
	height: 30px;
	border-radius: 6px;
	border: 2px solid rgba(255, 255, 255, 0.3);
}

.option input[type="range"] {
	flex: 1;
	margin: 0 15px;
	accent-color: #8f109b;
}

.option input[type="file"] {
	color: #ffffff;
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 6px;
	padding: 8px;
	margin-right: 10px;
}

.settings-footer {
	display: flex;
	justify-content: space-between;
	gap: 15px;
	margin-top: 20px;
	padding-top: 15px;
	border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.save-btn, .reset-btn {
	flex: 1;
	padding: 12px 20px;
	border: none;
	border-radius: 8px;
	font-size: 1.1em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	pointer-events: all;
	position: relative;
	z-index: 10001;
}

.save-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
}

.save-btn:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.reset-btn {
	background: linear-gradient(135deg, #f44336, #da190b);
	color: white;
}

.reset-btn:hover {
	background: linear-gradient(135deg, #da190b, #c62828);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
}

#reset-images {
	background: rgba(255, 165, 0, 0.2);
	border: 1px solid rgba(255, 165, 0, 0.5);
	color: #ffaa00;
	padding: 8px 15px;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.3s ease;
}

#reset-images:hover {
	background: rgba(255, 165, 0, 0.4);
}

/* نافذة اختيار السلاح */
.weapon-selection-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.9);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 2000;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.weapon-selection-container {
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(50, 50, 50, 0.95));
	border-radius: 15px;
	padding: 30px;
	max-width: 500px;
	width: 90%;
	border: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.weapon-selection-container h3 {
	color: #ffffff;
	text-align: center;
	margin-bottom: 20px;
	font-size: 1.4em;
}

.weapon-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: 10px;
	margin-bottom: 20px;
}

.weapon-btn {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: #ffffff;
	padding: 12px 16px;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	font-size: 1em;
}

.weapon-btn:hover {
	background: linear-gradient(135deg, rgba(143, 16, 155, 0.3), rgba(143, 16, 155, 0.1));
	border-color: rgba(143, 16, 155, 0.5);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(143, 16, 155, 0.3);
}

.modal-buttons {
	display: flex;
	justify-content: center;
	gap: 15px;
}

#cancel-weapon-selection {
	background: linear-gradient(135deg, #f44336, #da190b);
	border: none;
	color: white;
	padding: 10px 20px;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

#cancel-weapon-selection:hover {
	background: linear-gradient(135deg, #da190b, #c62828);
	transform: translateY(-2px);
}

/* إعدادات الأصوات */
.sound-options {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.toggle-switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 25px;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 25px;
	cursor: pointer;
	transition: all 0.3s ease;
	margin-left: 10px;
}

.toggle-switch::before {
	content: '';
	position: absolute;
	top: 2px;
	left: 2px;
	width: 21px;
	height: 21px;
	background: #ffffff;
	border-radius: 50%;
	transition: all 0.3s ease;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

#enable-sounds:checked + .toggle-switch {
	background: linear-gradient(135deg, #4CAF50, #45a049);
}

#enable-sounds:checked + .toggle-switch::before {
	transform: translateX(25px);
}

#enable-sounds {
	display: none;
}

.sound-upload-section {
	margin-top: 15px;
	padding: 15px;
	background: rgba(255, 255, 255, 0.03);
	border-radius: 10px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.sound-upload-section h4 {
	color: #ffffff;
	margin: 0 0 15px 0;
	font-size: 1.2em;
	font-weight: 500;
}

.sound-upload-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 15px;
	margin-bottom: 15px;
}

.sound-upload-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
	padding: 12px;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.sound-upload-item label {
	color: #ffffff;
	font-size: 1em;
	font-weight: 400;
}

.sound-upload-item input[type="file"] {
	color: #ffffff;
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 6px;
	padding: 6px;
	font-size: 0.9em;
}

.test-sound {
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
	border: 1px solid rgba(76, 175, 80, 0.5);
	color: #4CAF50;
	padding: 6px 12px;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 0.9em;
}

.test-sound:hover {
	background: linear-gradient(135deg, rgba(76, 175, 80, 0.5), rgba(76, 175, 80, 0.2));
	transform: translateY(-1px);
}

#reset-sounds {
	background: rgba(255, 165, 0, 0.2);
	border: 1px solid rgba(255, 165, 0, 0.5);
	color: #ffaa00;
	padding: 10px 20px;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	width: 100%;
}

#reset-sounds:hover {
	background: rgba(255, 165, 0, 0.4);
}

/* إحصائيات متقدمة */
.stats-container {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: 15px;
}

.stat-card {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
	border: 1px solid rgba(255, 255, 255, 0.15);
	border-radius: 12px;
	padding: 15px;
	display: flex;
	align-items: center;
	gap: 12px;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.stat-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(143, 16, 155, 0.1), transparent);
	transition: left 0.5s ease;
}

.stat-card:hover::before {
	left: 100%;
}

.stat-card:hover {
	border-color: rgba(143, 16, 155, 0.4);
	transform: translateY(-3px);
	box-shadow: 0 8px 25px rgba(143, 16, 155, 0.2);
}

.stat-icon {
	font-size: 2em;
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.stat-info {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.stat-number {
	font-size: 1.8em;
	font-weight: 700;
	color: #ffffff;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
	background: linear-gradient(135deg, #ffffff, #e0e0e0);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.stat-label {
	font-size: 0.9em;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 400;
}

.chart-container {
	background: rgba(255, 255, 255, 0.05);
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 12px;
	padding: 20px;
	text-align: center;
}

.chart-container h4 {
	color: #ffffff;
	margin: 0 0 15px 0;
	font-size: 1.2em;
	font-weight: 500;
}

#weapons-chart {
	max-width: 100%;
	height: auto;
	border-radius: 8px;
}

.stats-actions {
	display: flex;
	gap: 15px;
	justify-content: center;
}

.export-btn, .reset-stats-btn {
	padding: 10px 20px;
	border: none;
	border-radius: 8px;
	font-size: 1em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.export-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.export-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.reset-stats-btn {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
}

.reset-stats-btn:hover {
	background: linear-gradient(135deg, #F57C00, #E65100);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
}

/* إعدادات التصفية */
.filter-options {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.filter-group {
	display: flex;
	flex-direction: column;
	gap: 15px;
	padding: 15px;
	background: rgba(255, 255, 255, 0.03);
	border-radius: 10px;
	border: 1px solid rgba(255, 255, 255, 0.1);
	transition: all 0.3s ease;
}

.filter-group.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.filter-item {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.filter-item label {
	color: #ffffff;
	font-size: 1em;
	font-weight: 400;
}

.filter-item select {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 6px;
	padding: 8px;
	color: #ffffff;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.filter-item select option {
	background: rgba(30, 30, 30, 0.95);
	color: #ffffff;
}

.distance-filter {
	display: flex;
	align-items: center;
	gap: 10px;
	flex-wrap: wrap;
}

.distance-filter input[type="range"] {
	flex: 1;
	min-width: 100px;
	accent-color: #8f109b;
}

.distance-filter span {
	color: rgba(255, 255, 255, 0.8);
	font-size: 0.9em;
	white-space: nowrap;
}

.filter-actions {
	display: flex;
	gap: 15px;
	justify-content: center;
	margin-top: 15px;
}

.apply-btn, .reset-filter-btn {
	padding: 10px 20px;
	border: none;
	border-radius: 8px;
	font-size: 1em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.apply-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
}

.apply-btn:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.reset-filter-btn {
	background: linear-gradient(135deg, #FF5722, #D84315);
	color: white;
}

.reset-filter-btn:hover {
	background: linear-gradient(135deg, #D84315, #BF360C);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(255, 87, 34, 0.4);
}

/* تحسين التبديل للتصفية */
#enable-filters {
	display: none;
}

#hide-my-kills, #hide-my-deaths, #headshots-only, #multikill-only {
	display: none;
}

/* حاوية الإشعارات */
#notifications-container {
	position: fixed;
	top: 20px;
	right: 20px;
	width: 350px;
	z-index: 1500;
	pointer-events: none;
}

#notifications-container.top-left {
	top: 20px;
	left: 20px;
	right: auto;
}

#notifications-container.bottom-right {
	top: auto;
	bottom: 20px;
	right: 20px;
}

#notifications-container.bottom-left {
	top: auto;
	bottom: 20px;
	left: 20px;
	right: auto;
}

#notifications-container.center {
	top: 50%;
	left: 50%;
	right: auto;
	transform: translate(-50%, -50%);
}

/* الإشعار الفردي */
.notification {
	background: linear-gradient(135deg, rgba(30, 30, 30, 0.95), rgba(50, 50, 70, 0.9));
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 12px;
	padding: 15px;
	margin-bottom: 10px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(10px);
	animation: slideInNotification 0.4s ease-out;
	pointer-events: auto;
	position: relative;
	overflow: hidden;
}

.notification::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #4CAF50, #2196F3, #9C27B0);
	border-radius: 12px 12px 0 0;
}

.notification.killstreak::before {
	background: linear-gradient(90deg, #FF5722, #FF9800);
}

.notification.achievement::before {
	background: linear-gradient(90deg, #FFD700, #FFA000);
}

.notification.longshot::before {
	background: linear-gradient(90deg, #2196F3, #03A9F4);
}

.notification.revenge::before {
	background: linear-gradient(90deg, #F44336, #E91E63);
}

@keyframes slideInNotification {
	from {
		transform: translateX(100%);
		opacity: 0;
	}
	to {
		transform: translateX(0);
		opacity: 1;
	}
}

.notification-header {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 8px;
}

.notification-icon {
	font-size: 1.5em;
	filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
}

.notification-title {
	font-size: 1.1em;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.notification-message {
	color: rgba(255, 255, 255, 0.9);
	font-size: 0.95em;
	line-height: 1.4;
	margin: 0;
}

.notification-close {
	position: absolute;
	top: 8px;
	right: 8px;
	background: rgba(255, 255, 255, 0.1);
	border: none;
	color: rgba(255, 255, 255, 0.7);
	width: 20px;
	height: 20px;
	border-radius: 50%;
	cursor: pointer;
	font-size: 12px;
	transition: all 0.3s ease;
}

.notification-close:hover {
	background: rgba(255, 0, 0, 0.3);
	color: #ffffff;
}

/* إعدادات الإشعارات */
.notification-options {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.notification-group {
	display: flex;
	flex-direction: column;
	gap: 15px;
	padding: 15px;
	background: rgba(255, 255, 255, 0.03);
	border-radius: 10px;
	border: 1px solid rgba(255, 255, 255, 0.1);
	transition: all 0.3s ease;
}

.notification-group.disabled {
	opacity: 0.5;
	pointer-events: none;
}

.notification-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 10px;
}

.notification-item label {
	color: #ffffff;
	font-size: 1em;
	font-weight: 400;
	flex: 1;
}

.notification-item select {
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 6px;
	padding: 6px;
	color: #ffffff;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.notification-actions {
	display: flex;
	gap: 15px;
	justify-content: center;
}

.test-btn, .reset-notification-btn {
	padding: 10px 20px;
	border: none;
	border-radius: 8px;
	font-size: 1em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.test-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.test-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.reset-notification-btn {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
}

.reset-notification-btn:hover {
	background: linear-gradient(135deg, #F57C00, #E65100);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
}

#enable-notifications, #killstreak-notifications, #headshot-streak-notifications,
#longshot-notifications, #revenge-notifications, #multikill-notifications,
#achievement-notifications {
	display: none;
}

/* نظام التصدير والاستيراد */
.import-export-options {
	display: flex;
	flex-direction: column;
	gap: 25px;
}

.export-section, .import-section, .backup-section {
	padding: 20px;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 12px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.export-section h4, .import-section h4, .backup-section h4 {
	color: #ffffff;
	margin: 0 0 10px 0;
	font-size: 1.2em;
	font-weight: 600;
}

.export-section p, .import-section p {
	color: rgba(255, 255, 255, 0.8);
	margin: 0 0 15px 0;
	font-size: 0.95em;
}

.export-buttons {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.export-all-btn, .export-settings-btn, .export-images-btn {
	padding: 10px 15px;
	border: none;
	border-radius: 8px;
	font-size: 0.9em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.export-all-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
}

.export-all-btn:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.export-settings-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.export-settings-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.export-images-btn {
	background: linear-gradient(135deg, #9C27B0, #7B1FA2);
	color: white;
}

.export-images-btn:hover {
	background: linear-gradient(135deg, #7B1FA2, #6A1B9A);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(156, 39, 176, 0.4);
}

.import-controls {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.import-btn {
	background: linear-gradient(135deg, #FF9800, #F57C00);
	color: white;
	padding: 12px 20px;
	border: none;
	border-radius: 8px;
	font-size: 1em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.import-btn:hover {
	background: linear-gradient(135deg, #F57C00, #E65100);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
}

.import-options {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
	gap: 10px;
	padding: 15px;
	background: rgba(255, 255, 255, 0.03);
	border-radius: 8px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.import-options label {
	display: flex;
	align-items: center;
	gap: 8px;
	color: rgba(255, 255, 255, 0.9);
	font-size: 0.9em;
	cursor: pointer;
}

.import-options input[type="checkbox"] {
	accent-color: #4CAF50;
}

.import-execute-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
	padding: 12px 20px;
	border: none;
	border-radius: 8px;
	font-size: 1em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.import-execute-btn:disabled {
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.5);
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

.import-execute-btn:not(:disabled):hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.backup-controls {
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.backup-controls label {
	display: flex;
	align-items: center;
	gap: 8px;
	color: rgba(255, 255, 255, 0.9);
	font-size: 1em;
	cursor: pointer;
}

.backup-controls input[type="checkbox"] {
	accent-color: #2196F3;
}

.backup-btn, .restore-btn {
	padding: 10px 15px;
	border: none;
	border-radius: 8px;
	font-size: 0.9em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
}

.backup-btn {
	background: linear-gradient(135deg, #607D8B, #455A64);
	color: white;
}

.backup-btn:hover {
	background: linear-gradient(135deg, #455A64, #37474F);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(96, 125, 139, 0.4);
}

.restore-btn {
	background: linear-gradient(135deg, #795548, #5D4037);
	color: white;
}

.restore-btn:hover {
	background: linear-gradient(135deg, #5D4037, #4E342E);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(121, 85, 72, 0.4);
}

/* إعدادات الصور المخصصة المحسنة */
.image-upload-methods {
	display: flex;
	gap: 10px;
	margin-bottom: 20px;
}

.upload-method-btn {
	padding: 10px 20px;
	border: 2px solid rgba(255, 255, 255, 0.2);
	background: rgba(255, 255, 255, 0.1);
	color: rgba(255, 255, 255, 0.7);
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	font-weight: 600;
	pointer-events: all;
}

.upload-method-btn.active,
.upload-method-btn:hover {
	background: linear-gradient(135deg, #8f109b, #7a0e85);
	border-color: #8f109b;
	color: white;
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(143, 16, 155, 0.3);
}

.upload-section {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	border: 1px solid rgba(255, 255, 255, 0.1);
}

.upload-section.hidden {
	display: none;
}

#weapon-selector {
	width: 100%;
	padding: 12px;
	background: rgba(255, 255, 255, 0.1);
	border: 1px solid rgba(255, 255, 255, 0.2);
	border-radius: 8px;
	color: white;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	font-size: 1em;
	margin-bottom: 15px;
}

#weapon-selector option {
	background: #2a2a2a;
	color: white;
}

.image-preview, .images-preview {
	margin-top: 15px;
	padding: 15px;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 8px;
	border: 2px dashed rgba(255, 255, 255, 0.2);
	min-height: 100px;
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	align-items: center;
	justify-content: center;
}

.preview-image {
	max-width: 80px;
	max-height: 80px;
	border-radius: 8px;
	border: 2px solid rgba(255, 255, 255, 0.3);
	object-fit: contain;
	background: rgba(255, 255, 255, 0.1);
	padding: 5px;
}

.upload-instructions {
	background: rgba(33, 150, 243, 0.1);
	border: 1px solid rgba(33, 150, 243, 0.3);
	border-radius: 8px;
	padding: 15px;
	margin: 15px 0;
}

.upload-instructions p {
	margin: 0 0 10px 0;
	font-weight: 600;
	color: #2196F3;
}

.upload-instructions ul {
	margin: 0;
	padding-right: 20px;
	color: rgba(255, 255, 255, 0.8);
}

.upload-instructions li {
	margin-bottom: 5px;
	font-size: 0.9em;
}

.image-actions {
	display: flex;
	gap: 15px;
	justify-content: center;
	margin: 20px 0;
	flex-wrap: wrap;
}

.apply-images-btn, .reset-images-btn, .preview-images-btn {
	padding: 12px 20px;
	border: none;
	border-radius: 8px;
	font-size: 1em;
	font-weight: 600;
	cursor: pointer;
	transition: all 0.3s ease;
	font-family: 'Valo', Arial, Helvetica, sans-serif;
	pointer-events: all;
}

.apply-images-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
}

.apply-images-btn:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.reset-images-btn {
	background: linear-gradient(135deg, #f44336, #da190b);
	color: white;
}

.reset-images-btn:hover {
	background: linear-gradient(135deg, #da190b, #c62828);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
}

.preview-images-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
	color: white;
}

.preview-images-btn:hover {
	background: linear-gradient(135deg, #1976D2, #1565C0);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.saved-images-section {
	margin-top: 30px;
	padding-top: 20px;
	border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.saved-images-section h4 {
	color: #8f109b;
	margin-bottom: 15px;
	font-size: 1.1em;
}

.saved-images-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
	gap: 15px;
	max-height: 300px;
	overflow-y: auto;
	padding: 15px;
	background: rgba(0, 0, 0, 0.2);
	border-radius: 8px;
}

.saved-image-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 10px;
	background: rgba(255, 255, 255, 0.05);
	border-radius: 8px;
	border: 1px solid rgba(255, 255, 255, 0.1);
	transition: all 0.3s ease;
}

.saved-image-item:hover {
	background: rgba(255, 255, 255, 0.1);
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.saved-image-item img {
	width: 60px;
	height: 60px;
	object-fit: contain;
	border-radius: 6px;
	margin-bottom: 8px;
	background: rgba(255, 255, 255, 0.1);
	padding: 5px;
}

.saved-image-item .weapon-name {
	font-size: 0.8em;
	color: rgba(255, 255, 255, 0.8);
	text-align: center;
	word-break: break-word;
}

.saved-image-item .remove-image {
	background: rgba(255, 0, 0, 0.2);
	border: 1px solid rgba(255, 0, 0, 0.5);
	color: #ff4444;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	font-size: 0.8em;
	cursor: pointer;
	margin-top: 5px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.saved-image-item .remove-image:hover {
	background: rgba(255, 0, 0, 0.4);
	transform: scale(1.1);
}

/* إضافة pointer-events لجميع الأزرار */
button {
	pointer-events: all !important;
	position: relative;
	z-index: 10001;
}

/* تحسين عام للأزرار */
button:not(:disabled) {
	cursor: pointer;
}

button:disabled {
	cursor: not-allowed;
	opacity: 0.6;
}

/* تأكيد أن جميع عناصر التحكم تعمل */
input, select, textarea {
	pointer-events: all !important;
	position: relative;
	z-index: 10001;
}

/* إضافة CSS للمعاينة */
.preview-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin: 10px;
}

.preview-label {
	margin-top: 8px;
	font-size: 0.8em;
	color: rgba(255, 255, 255, 0.8);
	text-align: center;
	word-break: break-word;
}