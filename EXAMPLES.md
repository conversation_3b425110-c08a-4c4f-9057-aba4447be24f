# أمثلة الاستخدام - CB Killfeed المطور

## نظرة عامة

هذا الملف يحتوي على أمثلة عملية لاستخدام جميع ميزات CB Killfeed المطور.

## 🎯 أمثلة تخصيص الألوان

### المثال 1: ثيم أحمر وأسود
```javascript
// في قائمة الإعدادات
colorSettings = {
    normalColor: "rgba(20, 20, 20, 0.9)",      // أسود داكن
    killColor: "rgba(220, 20, 20, 0.8)",       // أحمر للقتل
    deathColor: "rgba(150, 150, 20, 0.8)",     // أصفر للموت
    textColor: "rgb(255, 255, 255)"            // أبيض للنص
};
```

### المثال 2: ثيم أزرق وفضي
```javascript
colorSettings = {
    normalColor: "rgba(30, 50, 80, 0.85)",     // أزرق داكن
    killColor: "rgba(0, 150, 255, 0.8)",       // أزرق فاتح للقتل
    deathColor: "rgba(255, 100, 100, 0.8)",    // أحمر فاتح للموت
    textColor: "rgb(200, 220, 255)"            // أزرق فاتح للنص
};
```

### المثال 3: ثيم ذهبي فاخر
```javascript
colorSettings = {
    normalColor: "rgba(40, 30, 20, 0.9)",      // بني داكن
    killColor: "rgba(255, 215, 0, 0.8)",       // ذهبي للقتل
    deathColor: "rgba(255, 140, 0, 0.8)",      // برتقالي للموت
    textColor: "rgb(255, 248, 220)"            // كريمي للنص
};
```

## 🔊 أمثلة الأصوات المخصصة

### المثال 1: أصوات لعبة Call of Duty
- `kill.mp3` - صوت "Enemy Down"
- `death.mp3` - صوت "You have been eliminated"
- `headshot.mp3` - صوت "Headshot!"
- `multikill.mp3` - صوت "Multi-kill!"

### المثال 2: أصوات كرتونية
- `kill.mp3` - صوت "Pow!"
- `death.mp3` - صوت "Ouch!"
- `headshot.mp3` - صوت "Bullseye!"
- `multikill.mp3` - صوت "Combo!"

### المثال 3: أصوات واقعية
- `kill.mp3` - صوت طلقة نارية
- `death.mp3` - صوت سقوط
- `headshot.mp3` - صوت طلقة دقيقة
- `multikill.mp3` - صوت تصفيق

## 🖼️ أمثلة الصور المخصصة

### المثال 1: أيقونات مينيماليستية
- استخدم أيقونات بسيطة بألوان موحدة
- حجم 64x64 بكسل
- خلفية شفافة
- ألوان متناسقة مع الثيم

### المثال 2: صور واقعية للأسلحة
- صور عالية الجودة للأسلحة الحقيقية
- حجم 128x128 بكسل
- خلفية شفافة أو سوداء
- إضاءة جيدة وزوايا واضحة

### المثال 3: رموز تعبيرية (Emoji)
- 🔫 للمسدسات
- 🏹 للقناصة
- 💣 للمتفجرات
- ⚔️ للأسلحة البيضاء

## 🔍 أمثلة التصفية

### المثال 1: عرض القناصة فقط
```javascript
filterSettings = {
    enabled: true,
    weaponTypes: ['sniper'],
    minDistance: 50,
    maxDistance: 500,
    hideMyKills: false,
    hideMyDeaths: false,
    headshotsOnly: false,
    multikillOnly: false
};
```

### المثال 2: عرض القتل القريب فقط
```javascript
filterSettings = {
    enabled: true,
    weaponTypes: ['all'],
    minDistance: 0,
    maxDistance: 25,
    hideMyKills: false,
    hideMyDeaths: false,
    headshotsOnly: false,
    multikillOnly: false
};
```

### المثال 3: عرض إنجازاتي فقط
```javascript
filterSettings = {
    enabled: true,
    weaponTypes: ['all'],
    minDistance: 0,
    maxDistance: 500,
    hideMyKills: false,
    hideMyDeaths: true,
    headshotsOnly: true,
    multikillOnly: false
};
```

## 🔔 أمثلة الإشعارات

### المثال 1: إعدادات للاعب المحترف
```javascript
notificationSettings = {
    enabled: true,
    killstreakNotifications: true,      // إشعارات القتل المتتالي
    headshotStreakNotifications: true,  // إشعارات الهيدشوت المتتالي
    longshotNotifications: true,        // إشعارات القتل البعيد
    revengeNotifications: true,         // إشعارات الانتقام
    multikillNotifications: true,       // إشعارات القتل المتعدد
    achievementNotifications: true,     // إشعارات الإنجازات
    duration: 3000,                     // 3 ثواني
    position: 'top-right'               // أعلى اليمين
};
```

### المثال 2: إعدادات للاعب العادي
```javascript
notificationSettings = {
    enabled: true,
    killstreakNotifications: true,
    headshotStreakNotifications: false,
    longshotNotifications: false,
    revengeNotifications: true,
    multikillNotifications: false,
    achievementNotifications: true,
    duration: 4000,                     // 4 ثواني
    position: 'center'                  // في الوسط
};
```

## 💾 أمثلة التصدير والاستيراد

### المثال 1: تصدير إعدادات فريق
```json
{
  "version": "1.0",
  "timestamp": "2025-06-27T10:30:00.000Z",
  "colors": {
    "normalColor": "rgba(30, 30, 30, 0.288)",
    "killColor": "rgba(0, 255, 0, 0.295)",
    "deathColor": "rgba(255, 0, 0, 0.247)",
    "textColor": "rgb(255, 255, 255)"
  },
  "sounds": {
    "enabled": true,
    "volume": 0.7,
    "customSounds": {}
  }
}
```

### المثال 2: استيراد انتقائي
- ✅ استيراد الألوان
- ✅ استيراد الأصوات
- ❌ عدم استيراد الصور
- ✅ استيراد التصفية
- ❌ عدم استيراد الإحصائيات

## 📊 أمثلة الإحصائيات

### المثال 1: لاعب قناص
```javascript
playerStats = {
    totalKills: 150,
    totalDeaths: 45,
    headshots: 120,                     // 80% هيدشوت
    bestStreak: 15,
    longestKill: 350,                   // 350 متر
    weaponStats: {
        "WEAPON_SNIPERRIFLE": 100,
        "WEAPON_MARKSMANRIFLE": 50
    }
};
// نسبة K/D: 3.33
// نسبة الهيدشوت: 80%
```

### المثال 2: لاعب هجومي
```javascript
playerStats = {
    totalKills: 300,
    totalDeaths: 200,
    headshots: 75,                      // 25% هيدشوت
    bestStreak: 8,
    longestKill: 50,                    // 50 متر
    weaponStats: {
        "WEAPON_ASSAULTRIFLE": 150,
        "WEAPON_SMG": 100,
        "WEAPON_SHOTGUN": 50
    }
};
// نسبة K/D: 1.50
// نسبة الهيدشوت: 25%
```

## 🎮 سيناريوهات الاستخدام

### السيناريو 1: خادم PvP
- تفعيل جميع الإشعارات
- ألوان زاهية ومميزة
- أصوات محفزة
- إحصائيات مفصلة
- تصفية حسب المسافة

### السيناريو 2: خادم RP
- إشعارات محدودة
- ألوان هادئة وواقعية
- أصوات واقعية
- إحصائيات بسيطة
- تصفية لإخفاء القتل غير المرغوب

### السيناريو 3: خادم تدريب
- تفعيل جميع الميزات
- ألوان تعليمية
- أصوات واضحة
- إحصائيات تفصيلية
- بدون تصفية

## 🔧 نصائح التخصيص

### نصيحة 1: اختيار الألوان
- استخدم ألوان متناسقة
- تجنب الألوان الزاهية جداً
- اختبر الألوان في بيئات مختلفة
- احفظ نسخة احتياطية من إعداداتك

### نصيحة 2: الأصوات المخصصة
- استخدم أصوات قصيرة (أقل من 3 ثواني)
- تأكد من جودة الصوت
- اختبر مستوى الصوت
- احفظ الأصوات الأصلية كنسخة احتياطية

### نصيحة 3: الصور المخصصة
- استخدم صور عالية الجودة
- حافظ على حجم صغير للملفات
- استخدم خلفية شفافة
- اختبر الصور في أحجام مختلفة

### نصيحة 4: الإحصائيات
- راجع إحصائياتك بانتظام
- صدر إحصائياتك كنسخة احتياطية
- استخدم الإحصائيات لتحسين أدائك
- شارك إحصائياتك مع الأصدقاء

## 🚀 أفكار إبداعية

### فكرة 1: ثيم الفصول
- ثيم شتوي: ألوان زرقاء وبيضاء
- ثيم صيفي: ألوان صفراء وبرتقالية
- ثيم خريفي: ألوان بنية وذهبية
- ثيم ربيعي: ألوان خضراء وزهرية

### فكرة 2: ثيم الفرق
- فريق أحمر: جميع العناصر حمراء
- فريق أزرق: جميع العناصر زرقاء
- فريق أخضر: جميع العناصر خضراء
- فريق أصفر: جميع العناصر صفراء

### فكرة 3: ثيم الألعاب
- ثيم Minecraft: ألوان بكسلية
- ثيم Fortnite: ألوان زاهية
- ثيم PUBG: ألوان واقعية
- ثيم Valorant: ألوان مستقبلية

---

**استمتع بالتخصيص واجعل تجربتك فريدة!**
**CB STORE - إبداع بلا حدود**
