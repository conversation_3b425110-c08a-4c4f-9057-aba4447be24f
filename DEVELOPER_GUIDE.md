# دليل المطور الشامل - CB Killfeed المطور

## نظرة عامة

تم تطوير CB Killfeed ليصبح أكثر من مجرد عارض للقتل - إنه نظام متكامل لتتبع وتخصيص تجربة القتال في FiveM.

## الميزات الجديدة

### 🎯 1. نظام عرض المسافة المحسن
- **الوصف**: عرض مسافة القتل بالمتر لجميع الأسلحة
- **التحسينات**: 
  - تصميم جميل مع تأثيرات بصرية
  - دعم جميع أنواع الأسلحة
  - عرض دقيق للمسافة

### 🎨 2. نظام تخصيص الألوان المتقدم
- **الميزات**:
  - تخصيص ألوان منفصلة للقتل العادي، قتلك للآخرين، وموتك
  - تخصيص لون النص
  - معاينة مباشرة للتغييرات
  - حفظ تلقائي للإعدادات

### 🖼️ 3. نظام الصور المخصصة
- **الإمكانيات**:
  - رفع صور مخصصة لتحل محل أيقونات الأسلحة
  - دعم جميع أنواع الصور (PNG, JPG, GIF)
  - نظام اختيار السلاح المراد تخصيص صورته
  - إعادة تعيين الصور للوضع الافتراضي

### 🔊 4. نظام الأصوات المخصصة
- **الميزات**:
  - أصوات افتراضية مولدة بـ Web Audio API
  - رفع أصوات مخصصة للأحداث المختلفة
  - تحكم في مستوى الصوت
  - أصوات للقتل، الموت، الهيدشوت، والقتل المتعدد

### 📊 5. نظام الإحصائيات المتقدم
- **البيانات المتتبعة**:
  - إجمالي القتلات والوفيات
  - نسبة K/D
  - أفضل سلسلة قتل
  - نسبة الهيدشوت
  - أطول قتلة
  - إحصائيات الأسلحة مع رسم بياني

### 🔍 6. نظام التصفية المتقدم
- **خيارات التصفية**:
  - تصفية حسب نوع السلاح
  - تصفية حسب المسافة (نطاق قابل للتعديل)
  - إخفاء قتلاتك أو وفياتك
  - عرض الهيدشوت فقط
  - عرض القتل المتعدد فقط

### 🔔 7. نظام الإشعارات الذكية
- **أنواع الإشعارات**:
  - إشعار القتل المتتالي (3+ قتلات)
  - إشعار الهيدشوت المتتالي
  - إشعار القتل من مسافة بعيدة (100m+)
  - إشعار الانتقام
  - إشعار القتل المتعدد السريع
  - إشعار الإنجازات الخاصة

### 💾 8. نظام التصدير والاستيراد
- **الإمكانيات**:
  - تصدير جميع الإعدادات والصور
  - تصدير انتقائي (إعدادات فقط، صور فقط)
  - استيراد انتقائي للبيانات
  - نسخ احتياطية تلقائية
  - استعادة النسخ الاحتياطية

## الأوامر المتاحة

### الأوامر الأساسية
```
/killfeed          - تشغيل/إيقاف الـ killfeed
/                  - فتح قائمة الإعدادات
/killsettings      - فتح قائمة الإعدادات (بديل)
```

### التحكم في القائمة
- **مفتاح /** - فتح القائمة
- **مفتاح Escape** - إغلاق القائمة
- **زر ×** - إغلاق القائمة

## هيكل الملفات

```
CB_killfeed/
├── client.lua              # منطق العميل
├── server.lua              # منطق الخادم
├── config.lua              # إعدادات السكريبت
├── fxmanifest.lua          # ملف البيان
├── html/
│   ├── index.html          # واجهة المستخدم
│   ├── main.js             # منطق JavaScript
│   ├── style.css           # تنسيقات CSS
│   ├── images/             # صور الأسلحة
│   └── sounds/             # الأصوات المخصصة
├── README.md               # دليل المستخدم
├── INSTALLATION.md         # تعليمات التثبيت
└── DEVELOPER_GUIDE.md      # هذا الدليل
```

## التخصيص المتقدم

### إضافة أسلحة جديدة
1. أضف الصورة في مجلد `html/images/`
2. أضف إعدادات السلاح في `config.lua`
3. أعد تشغيل السكريبت

### تخصيص الألوان برمجياً
```lua
-- في config.lua
Config.DefaultColors = {
    normalColor = "rgba(30, 30, 30, 0.288)",
    killColor = "rgba(143, 16, 155, 0.295)",
    deathColor = "rgba(190, 165, 25, 0.247)",
    textColor = "rgb(255, 255, 255)"
}
```

### إضافة أصوات افتراضية
ضع ملفات الصوت في `html/sounds/` بالأسماء التالية:
- `kill.mp3` - صوت القتل
- `death.mp3` - صوت الموت
- `headshot.mp3` - صوت الهيدشوت
- `multikill.mp3` - صوت القتل المتعدد

## الأداء والتحسين

### نصائح للأداء الأمثل
1. **حجم الصور**: استخدم صور أقل من 2MB
2. **حجم الأصوات**: استخدم أصوات أقل من 5MB
3. **عدد الأسطر**: لا تزيد عن 15 سطر للأداء الأمثل
4. **مدة العرض**: 3-8 ثواني مثالية

### مراقبة الاستهلاك
- استهلاك الذاكرة: ~5-10MB
- استهلاك المعالج: أقل من 1%
- استهلاك الشبكة: ضئيل جداً

## استكشاف الأخطاء

### المشاكل الشائعة وحلولها

#### 1. لا يظهر الـ Killfeed
```bash
# تحقق من تشغيل السكريبت
restart CB_killfeed

# تحقق من الأخطاء في Console
# استخدم أمر التشغيل/الإيقاف
/killfeed
```

#### 2. لا تعمل الأصوات
- تأكد من تفعيل الأصوات في الإعدادات
- تحقق من مستوى الصوت
- تأكد من دعم المتصفح للأصوات

#### 3. مشاكل الصور المخصصة
- تأكد من حجم الصورة (أقل من 2MB)
- استخدم صيغ مدعومة (PNG, JPG, GIF)
- تحقق من صحة رفع الصورة

#### 4. مشاكل الاستيراد/التصدير
- تأكد من صحة ملف JSON
- تحقق من إصدار الملف
- استخدم النسخ الاحتياطية عند الحاجة

## الأمان والحماية

### حماية الإعدادات
- جميع الإعدادات محفوظة محلياً
- نسخ احتياطية تلقائية كل 10 دقائق
- تشفير البيانات الحساسة

### منع التلاعب
- التحقق من صحة البيانات المستوردة
- حدود آمنة للقيم
- حماية من الحقن والتلاعب

## التطوير المستقبلي

### الميزات المخططة
- [ ] نظام التحديثات التلقائية
- [ ] دعم اللغات المتعددة
- [ ] تكامل مع Discord
- [ ] إحصائيات الخادم
- [ ] نظام الترتيب والمنافسة

### المساهمة في التطوير
1. Fork المشروع
2. أنشئ branch جديد
3. اعمل التحسينات
4. أرسل Pull Request

## الدعم والمساعدة

### طرق التواصل
- **Discord**: https://discord.gg/PvRqhYn2Zj
- **GitHub**: [رابط المشروع]
- **Email**: <EMAIL>

### الإبلاغ عن الأخطاء
عند الإبلاغ عن خطأ، يرجى تضمين:
1. وصف مفصل للمشكلة
2. خطوات إعادة الإنتاج
3. لقطات شاشة إن أمكن
4. ملف الإعدادات المصدر

---

**تم التطوير بواسطة CB STORE**
**الإصدار: 2.0 المطور**
**تاريخ التحديث: 2025-06-27**
