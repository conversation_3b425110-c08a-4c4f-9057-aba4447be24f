let showTime = 7500;
let maxLines = 8;
let textColor = "255, 255, 255";

// إعدادات الألوان
let colorSettings = {
	normalColor: "rgba(30, 30, 30, 0.288)",
	killColor: "rgba(143, 16, 155, 0.295)",
	deathColor: "rgba(190, 165, 25, 0.247)",
	textColor: "rgb(255, 255, 255)"
};

// إعدادات مخصصة للصور
let customImages = {};

// إعدادات الأصوات
let soundSettings = {
	enabled: true,
	volume: 0.5,
	customSounds: {}
};

// متغيرات تتبع القتل
let killStreak = 0;
let lastKiller = null;
let lastVictim = null;

// إحصائيات اللاعب
let playerStats = {
	totalKills: 0,
	totalDeaths: 0,
	headshots: 0,
	bestStreak: 0,
	currentStreak: 0,
	longestKill: 0,
	weaponStats: {},
	sessionStart: Date.now()
};

// إعدادات التصفية
let filterSettings = {
	enabled: false,
	weaponTypes: ['all'],
	minDistance: 0,
	maxDistance: 500,
	hideMyKills: false,
	hideMyDeaths: false,
	headshotsOnly: false,
	multikillOnly: false
};

// إعدادات الإشعارات
let notificationSettings = {
	enabled: true,
	killstreakNotifications: true,
	headshotStreakNotifications: true,
	longshotNotifications: true,
	revengeNotifications: true,
	multikillNotifications: true,
	achievementNotifications: true,
	duration: 4000,
	position: 'top-right'
};

// متغيرات تتبع الإشعارات
let recentKillers = {};
let headshotStreak = 0;
let lastKillTime = 0;

window.onload = (e) => {
	window.addEventListener('message', onEventRecieved);
	initializeSettings();
};

// تهيئة الإعدادات
function initializeSettings() {
	loadSettings();
	setupSettingsEventListeners();
	updateColorPreviews();
	updateStatsDisplay();
}

// تحميل الإعدادات المحفوظة
function loadSettings() {
	const savedSettings = localStorage.getItem('killfeed-settings');
	if (savedSettings) {
		const settings = JSON.parse(savedSettings);
		colorSettings = { ...colorSettings, ...settings.colors };
		showTime = settings.showTime || showTime;
		maxLines = settings.maxLines || maxLines;
		customImages = settings.customImages || {};
		soundSettings = { ...soundSettings, ...settings.sounds };
		playerStats = { ...playerStats, ...settings.stats };
		filterSettings = { ...filterSettings, ...settings.filters };
		notificationSettings = { ...notificationSettings, ...settings.notifications };
	}
}

// حفظ الإعدادات
function saveSettings() {
	const settings = {
		colors: colorSettings,
		showTime: showTime,
		maxLines: maxLines,
		customImages: customImages,
		sounds: soundSettings,
		stats: playerStats,
		filters: filterSettings,
		notifications: notificationSettings
	};
	localStorage.setItem('killfeed-settings', JSON.stringify(settings));
}

// إعداد مستمعي الأحداث للإعدادات
function setupSettingsEventListeners() {
	// فتح وإغلاق القائمة
	document.addEventListener('keydown', function(e) {
		// التحقق من أن القائمة مغلقة قبل فتحها بمفتاح /
		if (e.key === '/' && document.getElementById('settings-menu').classList.contains('hidden')) {
			e.preventDefault();
			toggleSettingsMenu();
		}
		if (e.key === 'Escape') {
			closeSettingsMenu();
		}
	});

	// أزرار الإغلاق والحفظ
	document.getElementById('close-settings').addEventListener('click', closeSettingsMenu);
	document.getElementById('save-settings').addEventListener('click', saveSettingsAndClose);
	document.getElementById('reset-settings').addEventListener('click', resetSettings);
	document.getElementById('reset-images').addEventListener('click', resetCustomImages);

	// مستمعي تغيير الألوان
	document.getElementById('normal-color').addEventListener('change', updateNormalColor);
	document.getElementById('kill-color').addEventListener('change', updateKillColor);
	document.getElementById('death-color').addEventListener('change', updateDeathColor);
	document.getElementById('text-color').addEventListener('change', updateTextColor);

	// مستمعي الإعدادات العامة
	document.getElementById('show-time').addEventListener('input', updateShowTime);
	document.getElementById('max-lines').addEventListener('input', updateMaxLines);

	// رفع الصور المخصصة
	document.getElementById('custom-image').addEventListener('change', handleCustomImageUpload);

	// مستمعي الأصوات
	document.getElementById('enable-sounds').addEventListener('change', updateSoundEnabled);
	document.getElementById('sound-volume').addEventListener('input', updateSoundVolume);
	document.getElementById('reset-sounds').addEventListener('click', resetCustomSounds);

	// رفع الأصوات المخصصة
	document.getElementById('kill-sound').addEventListener('change', (e) => handleCustomSoundUpload(e, 'kill'));
	document.getElementById('death-sound').addEventListener('change', (e) => handleCustomSoundUpload(e, 'death'));
	document.getElementById('headshot-sound').addEventListener('change', (e) => handleCustomSoundUpload(e, 'headshot'));
	document.getElementById('multikill-sound').addEventListener('change', (e) => handleCustomSoundUpload(e, 'multikill'));

	// أزرار تجربة الأصوات
	document.querySelectorAll('.test-sound').forEach(btn => {
		btn.addEventListener('click', (e) => testSound(e.target.dataset.sound));
	});

	// مستمعي الإحصائيات
	document.getElementById('export-stats').addEventListener('click', exportStats);
	document.getElementById('reset-stats').addEventListener('click', resetStats);

	// مستمعي التصفية
	document.getElementById('enable-filters').addEventListener('change', toggleFilters);
	document.getElementById('min-distance').addEventListener('input', updateMinDistance);
	document.getElementById('max-distance').addEventListener('input', updateMaxDistance);
	document.getElementById('apply-filters').addEventListener('click', applyFilters);
	document.getElementById('reset-filters').addEventListener('click', resetFilters);

	// مستمعي الإشعارات
	document.getElementById('enable-notifications').addEventListener('change', toggleNotifications);
	document.getElementById('notification-duration').addEventListener('input', updateNotificationDuration);
	document.getElementById('notification-position').addEventListener('change', updateNotificationPosition);
	document.getElementById('test-notification').addEventListener('click', testNotification);
	document.getElementById('reset-notifications').addEventListener('click', resetNotifications);
}

// فتح/إغلاق قائمة الإعدادات
function toggleSettingsMenu() {
	const menu = document.getElementById('settings-menu');
	menu.classList.toggle('hidden');
}

function closeSettingsMenu() {
	document.getElementById('settings-menu').classList.add('hidden');
	// إرسال إشارة إغلاق للعميل
	fetch(`https://${GetParentResourceName()}/closeSettings`, {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify({})
	});
}

function saveSettingsAndClose() {
	saveSettings();
	closeSettingsMenu();
	// إرسال الإعدادات الجديدة للعميل
	fetch(`https://${GetParentResourceName()}/updateSettings`, {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify({
			colors: colorSettings,
			showTime: showTime,
			maxLines: maxLines
		})
	});
}

// وظيفة للحصول على اسم المورد
function GetParentResourceName() {
	return 'CB_killfeed';
}

// تحديث معاينات الألوان
function updateColorPreviews() {
	document.getElementById('normal-preview').style.backgroundColor = colorSettings.normalColor;
	document.getElementById('kill-preview').style.backgroundColor = colorSettings.killColor;
	document.getElementById('death-preview').style.backgroundColor = colorSettings.deathColor;
	document.getElementById('text-preview').style.backgroundColor = colorSettings.textColor;
}

// وظائف تحديث الألوان
function updateNormalColor(e) {
	const color = hexToRgba(e.target.value, 0.288);
	colorSettings.normalColor = color;
	updateColorPreviews();
	showPreviewKill('normal');
}

function updateKillColor(e) {
	const color = hexToRgba(e.target.value, 0.295);
	colorSettings.killColor = color;
	updateColorPreviews();
	showPreviewKill('kill');
}

function updateDeathColor(e) {
	const color = hexToRgba(e.target.value, 0.247);
	colorSettings.deathColor = color;
	updateColorPreviews();
	showPreviewKill('death');
}

function updateTextColor(e) {
	const color = `rgb(${hexToRgb(e.target.value).join(', ')})`;
	colorSettings.textColor = color;
	textColor = hexToRgb(e.target.value).join(', ');
	updateColorPreviews();
	showPreviewKill('text');
}

// عرض معاينة مباشرة للتغييرات
function showPreviewKill(type) {
	const previewData = {
		id: 'preview_' + Date.now(),
		killer: { name: 'أنت', netId: 1 },
		victim: { name: 'عدو', netId: 2 },
		image: 'WEAPON_ASSAULTRIFLE',
		design: type === 'kill' ? 'teal-design' : (type === 'death' ? 'red-design' : 'black-design'),
		noScoped: false,
		headshot: true,
		driveBy: false,
		dist: '25m'
	};

	addKill(previewData.id, previewData.killer, previewData.victim, previewData.image,
		   previewData.design, previewData.noScoped, previewData.headshot,
		   previewData.driveBy, previewData.dist);
}

// تحويل HEX إلى RGBA
function hexToRgba(hex, alpha) {
	const rgb = hexToRgb(hex);
	return `rgba(${rgb.join(', ')}, ${alpha})`;
}

// تحويل HEX إلى RGB
function hexToRgb(hex) {
	const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
	return result ? [
		parseInt(result[1], 16),
		parseInt(result[2], 16),
		parseInt(result[3], 16)
	] : [255, 255, 255];
}

// تحديث الإعدادات العامة
function updateShowTime(e) {
	showTime = parseFloat(e.target.value) * 1000;
	document.getElementById('show-time-value').textContent = e.target.value + 's';
}

function updateMaxLines(e) {
	maxLines = parseInt(e.target.value);
	document.getElementById('max-lines-value').textContent = e.target.value;
}

// إعادة تعيين الإعدادات
function resetSettings() {
	colorSettings = {
		normalColor: "rgba(30, 30, 30, 0.288)",
		killColor: "rgba(143, 16, 155, 0.295)",
		deathColor: "rgba(190, 165, 25, 0.247)",
		textColor: "rgb(255, 255, 255)"
	};
	soundSettings = {
		enabled: true,
		volume: 0.5,
		customSounds: {}
	};
	showTime = 7500;
	maxLines = 8;
	textColor = "255, 255, 255";

	// تحديث واجهة المستخدم
	document.getElementById('normal-color').value = '#1e1e1e';
	document.getElementById('kill-color').value = '#8f109b';
	document.getElementById('death-color').value = '#bea519';
	document.getElementById('text-color').value = '#ffffff';
	document.getElementById('show-time').value = '7.5';
	document.getElementById('max-lines').value = '8';
	document.getElementById('show-time-value').textContent = '7.5s';
	document.getElementById('max-lines-value').textContent = '8';
	document.getElementById('enable-sounds').checked = true;
	document.getElementById('sound-volume').value = '50';
	document.getElementById('sound-volume-value').textContent = '50%';

	updateColorPreviews();
	resetCustomSounds();
	saveSettings();
}

// إعادة تعيين الصور المخصصة
function resetCustomImages() {
	customImages = {};
	saveSettings();
}

// رفع الصور المخصصة
function handleCustomImageUpload(e) {
	const file = e.target.files[0];
	if (file) {
		// التحقق من نوع الملف
		if (!file.type.startsWith('image/')) {
			alert('يرجى اختيار ملف صورة صالح');
			return;
		}

		// التحقق من حجم الملف (أقل من 2MB)
		if (file.size > 2 * 1024 * 1024) {
			alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 2MB');
			return;
		}

		const reader = new FileReader();
		reader.onload = function(event) {
			const imageData = event.target.result;

			// إنشاء نافذة لاختيار السلاح
			showWeaponSelectionModal(imageData);
		};
		reader.readAsDataURL(file);
	}
}

// عرض نافذة اختيار السلاح
function showWeaponSelectionModal(imageData) {
	const modal = document.createElement('div');
	modal.className = 'weapon-selection-modal';
	modal.innerHTML = `
		<div class="weapon-selection-container">
			<h3>اختر السلاح لتخصيص صورته</h3>
			<div class="weapon-grid">
				<button class="weapon-btn" data-weapon="WEAPON_PISTOL">مسدس</button>
				<button class="weapon-btn" data-weapon="WEAPON_ASSAULTRIFLE">بندقية هجومية</button>
				<button class="weapon-btn" data-weapon="WEAPON_SNIPERRIFLE">قناص</button>
				<button class="weapon-btn" data-weapon="WEAPON_SHOTGUN">بندقية</button>
				<button class="weapon-btn" data-weapon="WEAPON_SMG">رشاش صغير</button>
				<button class="weapon-btn" data-weapon="WEAPON_KNIFE">سكين</button>
				<button class="weapon-btn" data-weapon="explosion">انفجار</button>
				<button class="weapon-btn" data-weapon="vehicle">مركبة</button>
			</div>
			<div class="modal-buttons">
				<button id="cancel-weapon-selection">إلغاء</button>
			</div>
		</div>
	`;

	document.body.appendChild(modal);

	// إضافة مستمعي الأحداث
	modal.querySelectorAll('.weapon-btn').forEach(btn => {
		btn.addEventListener('click', function() {
			const weaponName = this.dataset.weapon;
			customImages[weaponName] = imageData;
			saveSettings();
			document.body.removeChild(modal);
			alert(`تم تخصيص صورة ${weaponName} بنجاح!`);
		});
	});

	modal.querySelector('#cancel-weapon-selection').addEventListener('click', function() {
		document.body.removeChild(modal);
	});
}

// وظائف إدارة الأصوات
function updateSoundEnabled(e) {
	soundSettings.enabled = e.target.checked;
	saveSettings();
}

function updateSoundVolume(e) {
	soundSettings.volume = parseFloat(e.target.value) / 100;
	document.getElementById('sound-volume-value').textContent = e.target.value + '%';
	saveSettings();
}

function handleCustomSoundUpload(e, soundType) {
	const file = e.target.files[0];
	if (file) {
		// التحقق من نوع الملف
		if (!file.type.startsWith('audio/')) {
			alert('يرجى اختيار ملف صوتي صالح');
			return;
		}

		// التحقق من حجم الملف (أقل من 5MB)
		if (file.size > 5 * 1024 * 1024) {
			alert('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 5MB');
			return;
		}

		const reader = new FileReader();
		reader.onload = function(event) {
			const audioData = event.target.result;
			soundSettings.customSounds[soundType] = audioData;
			saveSettings();
			alert(`تم رفع صوت ${getSoundTypeName(soundType)} بنجاح!`);
		};
		reader.readAsDataURL(file);
	}
}

function getSoundTypeName(type) {
	const names = {
		'kill': 'القتل',
		'death': 'الموت',
		'headshot': 'الهيدشوت',
		'multikill': 'القتل المتعدد'
	};
	return names[type] || type;
}

function testSound(soundType) {
	if (!soundSettings.enabled) {
		alert('الأصوات معطلة. يرجى تفعيلها أولاً');
		return;
	}

	playSound(soundType);
}

function playSound(soundType) {
	if (!soundSettings.enabled) return;

	// التحقق من وجود صوت مخصص
	if (soundSettings.customSounds[soundType]) {
		const audio = new Audio(soundSettings.customSounds[soundType]);
		audio.volume = soundSettings.volume;
		audio.play().catch(e => {
			console.log('لا يمكن تشغيل الصوت المخصص:', e);
		});
	} else {
		// تشغيل أصوات افتراضية باستخدام Web Audio API
		playDefaultSound(soundType);
	}
}

// تشغيل الأصوات الافتراضية
function playDefaultSound(soundType) {
	try {
		const audioContext = new (window.AudioContext || window.webkitAudioContext)();
		const oscillator = audioContext.createOscillator();
		const gainNode = audioContext.createGain();

		oscillator.connect(gainNode);
		gainNode.connect(audioContext.destination);

		// تحديد خصائص الصوت حسب النوع
		switch(soundType) {
			case 'kill':
				oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
				oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
				gainNode.gain.setValueAtTime(soundSettings.volume * 0.3, audioContext.currentTime);
				gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
				oscillator.type = 'square';
				break;

			case 'death':
				oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
				oscillator.frequency.exponentialRampToValueAtTime(100, audioContext.currentTime + 0.3);
				gainNode.gain.setValueAtTime(soundSettings.volume * 0.2, audioContext.currentTime);
				gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.4);
				oscillator.type = 'sawtooth';
				break;

			case 'headshot':
				oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
				oscillator.frequency.exponentialRampToValueAtTime(600, audioContext.currentTime + 0.05);
				oscillator.frequency.exponentialRampToValueAtTime(300, audioContext.currentTime + 0.15);
				gainNode.gain.setValueAtTime(soundSettings.volume * 0.4, audioContext.currentTime);
				gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
				oscillator.type = 'triangle';
				break;

			case 'multikill':
				// صوت متعدد النغمات
				for(let i = 0; i < 3; i++) {
					setTimeout(() => {
						const osc = audioContext.createOscillator();
						const gain = audioContext.createGain();
						osc.connect(gain);
						gain.connect(audioContext.destination);

						osc.frequency.setValueAtTime(600 + (i * 200), audioContext.currentTime);
						gain.gain.setValueAtTime(soundSettings.volume * 0.2, audioContext.currentTime);
						gain.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
						osc.type = 'sine';

						osc.start();
						osc.stop(audioContext.currentTime + 0.1);
					}, i * 50);
				}
				return; // الخروج مبكراً لأننا أنشأنا أصوات متعددة

			default:
				oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
				gainNode.gain.setValueAtTime(soundSettings.volume * 0.1, audioContext.currentTime);
				gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
		}

		oscillator.start();
		oscillator.stop(audioContext.currentTime + 0.5);

	} catch(e) {
		console.log('لا يمكن تشغيل الصوت الافتراضي:', e);
	}
}

function resetCustomSounds() {
	soundSettings.customSounds = {};
	saveSettings();

	// إعادة تعيين حقول الرفع
	document.getElementById('kill-sound').value = '';
	document.getElementById('death-sound').value = '';
	document.getElementById('headshot-sound').value = '';
	document.getElementById('multikill-sound').value = '';

	alert('تم إعادة تعيين جميع الأصوات المخصصة');
}

// معالجة أصوات القتل
function handleKillSounds(killer, victim, headshot, design) {
	if (!soundSettings.enabled) return;

	// تحديد نوع الحدث
	let soundType = null;

	if (design === 'teal-design') {
		// أنت قتلت شخصاً
		soundType = 'kill';

		// تتبع القتل المتتالي
		if (lastKiller === killer.name) {
			killStreak++;
			if (killStreak >= 3) {
				soundType = 'multikill';
			}
		} else {
			killStreak = 1;
		}
		lastKiller = killer.name;

		// صوت الهيدشوت له أولوية
		if (headshot) {
			soundType = 'headshot';
		}

	} else if (design === 'red-design') {
		// أنت مت
		soundType = 'death';
		killStreak = 0; // إعادة تعيين القتل المتتالي
	}

	// تشغيل الصوت
	if (soundType) {
		setTimeout(() => {
			playSound(soundType);
		}, 100); // تأخير بسيط لتجنب التداخل
	}
}

function AppendToFeed(id, string) {
	if ($("#killfeed-container").children().length >= maxLines) {
		$("#killfeed-container").children().first().remove();
	};

	$("#killfeed-container").append(string);

	let lineContainer = $('.kill-container[data-id=' + id + ']');
	let killLine = $('.kill-line[data-line-id=' + id + ']');

	lineContainer.hide().fadeIn(100).delay(showTime+600).fadeOut();
	setTimeout(function(){
		killLine.addClass("animate__animated");
		killLine.addClass("animate__flipOutX");
		setTimeout(function(){
			killLine.remove();
		}, 600); // 500 (+ 100 for margin)
	}, showTime);  // time it lasts
};

function addKill(data) {
	const id = data.id;
	const image = data.image;
	const design = data.design;
	const noScoped = data.noScoped;
	const headshot = data.headshot;
	const driveBy = data.driveBy;
	const dist = data.dist;

	let victim = data.victim;
	let killer = data.killer;

	// فحص التصفية
	if (!shouldShowKill(killer, victim, headshot, design, image, dist)) {
		return; // لا تعرض هذا القتل
	}

	// تشغيل الأصوات المناسبة
	handleKillSounds(killer, victim, headshot, design);

	// معالجة الإشعارات
	handleKillNotifications(killer, victim, headshot, design, image, dist);

	// تحديث الإحصائيات
	updatePlayerStats(killer, victim, headshot, design, image, dist);

	let duplicate = $(`.kill-line[data-line-id=${id}]`);
	if (duplicate.length > 0) {
		duplicate.remove();
	};

	// تحديد اللون المناسب بناءً على التصميم
	let backgroundColor = colorSettings.normalColor;
	if (design === 'teal-design') {
		backgroundColor = colorSettings.killColor;
	} else if (design === 'red-design') {
		backgroundColor = colorSettings.deathColor;
	}

	// string creation
	let appendString = `<div data-line-id="${id}" class="kill-line animate__animated animate__fadeInRight"><div data-id="${id}" class="kill-container" style="background-color: ${backgroundColor}; border-radius: 0.5em; padding: 0.3em; box-shadow: rgba(42, 42, 42, 0.692) 0px 4px 12px;">`;

	if (killer.name != undefined) {
		if (killer.tag != undefined) {
			appendString = appendString + `<p class="text tag" style="color: ${colorSettings.textColor};">${killer.tag}</p>`;
		};
		appendString = appendString + `<p class="text line-clamp name" style="color: ${colorSettings.textColor};">${killer.name}</p>`;
	} else {
		appendString = appendString + '<p class="none"></p>';
	};
	
	if (image != undefined) {
		// التحقق من وجود صورة مخصصة
		if (customImages[image]) {
			appendString = appendString + `<img src="${customImages[image]}" alt="${image}" class="weapon-image">`;
		} else {
			appendString = appendString + `<img src="images/${image}.png" alt="${image}" class="weapon-image">`;
		}
	};

	if (noScoped == true) {
		appendString = appendString + '<img src="images/noscoped.png" alt="noscoped" class="icon-image">';
	};

	if (driveBy != false && driveBy != undefined) {
		appendString = appendString + `<img src="images/${driveBy}.png" alt="driveBy" class="icon-image">`;
	};

	if (headshot == true) {
		appendString = appendString + '<img src="images/headshot.png" alt="headshot" class="icon-image">';
	};

	if (victim.name != undefined) {
		if (victim.tag != undefined) {
			appendString = appendString + `<p class="text tag" style="color: ${colorSettings.textColor};">${victim.tag}</p>`;
		};
		appendString = appendString + `<p class="text line-clamp name" style="color: ${colorSettings.textColor};">${victim.name}</p>`;

		if (dist != false && dist != undefined) {
			appendString = appendString + `<p class="text dist" style="color: ${colorSettings.textColor};" title="مسافة القتل">${dist}</p>`;
		};
	};

	appendString = appendString + '</div></div>';
	
	AppendToFeed(id, appendString)
};

function onEventRecieved(info) {
	let event = info.data;

	if (event.action == "addKill") {
		if (event.data == undefined) {
			console.error("event.data was nil!");
			return;
		}
		addKill(event.data);
	} else if (event.action == "toggleKillfeed") {
		if (event.data.state == true) {
			$("#killfeed-container").show()
		} else {
			$("#killfeed-container").hide()
		}
	} else if (event.action == "setConfig") {
		showTime = event.data.showTime;
		maxLines = event.data.maxLines;
	} else if (event.action == "openSettings") {
		document.getElementById('settings-menu').classList.remove('hidden');
	} else {
		console.log("event.action was not specified");
	}
};

// وظائف إدارة الإحصائيات
function updatePlayerStats(killer, victim, headshot, design, weapon, distance) {
	// تحديث إحصائيات القتل والموت
	if (design === 'teal-design') {
		// أنت قتلت شخصاً
		playerStats.totalKills++;
		playerStats.currentStreak++;

		if (playerStats.currentStreak > playerStats.bestStreak) {
			playerStats.bestStreak = playerStats.currentStreak;
		}

		// إحصائيات الهيدشوت
		if (headshot) {
			playerStats.headshots++;
		}

		// إحصائيات الأسلحة
		if (weapon) {
			if (!playerStats.weaponStats[weapon]) {
				playerStats.weaponStats[weapon] = 0;
			}
			playerStats.weaponStats[weapon]++;
		}

		// أطول قتلة
		if (distance) {
			const distanceNum = parseInt(distance.replace('m', ''));
			if (distanceNum > playerStats.longestKill) {
				playerStats.longestKill = distanceNum;
			}
		}

	} else if (design === 'red-design') {
		// أنت مت
		playerStats.totalDeaths++;
		playerStats.currentStreak = 0;
	}

	// حفظ الإحصائيات وتحديث العرض
	saveSettings();
	updateStatsDisplay();
}

function updateStatsDisplay() {
	// تحديث الأرقام
	document.getElementById('total-kills').textContent = playerStats.totalKills;
	document.getElementById('total-deaths').textContent = playerStats.totalDeaths;

	// حساب نسبة K/D
	const kdRatio = playerStats.totalDeaths > 0 ?
		(playerStats.totalKills / playerStats.totalDeaths).toFixed(2) :
		playerStats.totalKills.toFixed(2);
	document.getElementById('kd-ratio').textContent = kdRatio;

	document.getElementById('best-streak').textContent = playerStats.bestStreak;

	// حساب نسبة الهيدشوت
	const headshotPercentage = playerStats.totalKills > 0 ?
		((playerStats.headshots / playerStats.totalKills) * 100).toFixed(1) :
		'0';
	document.getElementById('headshot-percentage').textContent = headshotPercentage + '%';

	document.getElementById('longest-kill').textContent = playerStats.longestKill + 'm';

	// تحديث الرسم البياني
	updateWeaponsChart();
}

function updateWeaponsChart() {
	const canvas = document.getElementById('weapons-chart');
	const ctx = canvas.getContext('2d');

	// مسح الرسم السابق
	ctx.clearRect(0, 0, canvas.width, canvas.height);

	// الحصول على أفضل 5 أسلحة
	const sortedWeapons = Object.entries(playerStats.weaponStats)
		.sort(([,a], [,b]) => b - a)
		.slice(0, 5);

	if (sortedWeapons.length === 0) {
		ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
		ctx.font = '16px Valo';
		ctx.textAlign = 'center';
		ctx.fillText('لا توجد بيانات أسلحة بعد', canvas.width / 2, canvas.height / 2);
		return;
	}

	// رسم الأعمدة
	const barWidth = canvas.width / sortedWeapons.length - 20;
	const maxValue = Math.max(...sortedWeapons.map(([,count]) => count));

	sortedWeapons.forEach(([weapon, count], index) => {
		const barHeight = (count / maxValue) * (canvas.height - 60);
		const x = index * (barWidth + 20) + 10;
		const y = canvas.height - barHeight - 30;

		// رسم العمود
		const gradient = ctx.createLinearGradient(0, y, 0, y + barHeight);
		gradient.addColorStop(0, 'rgba(143, 16, 155, 0.8)');
		gradient.addColorStop(1, 'rgba(143, 16, 155, 0.4)');

		ctx.fillStyle = gradient;
		ctx.fillRect(x, y, barWidth, barHeight);

		// رسم الحدود
		ctx.strokeStyle = 'rgba(143, 16, 155, 1)';
		ctx.lineWidth = 2;
		ctx.strokeRect(x, y, barWidth, barHeight);

		// رسم النص
		ctx.fillStyle = '#ffffff';
		ctx.font = '12px Valo';
		ctx.textAlign = 'center';

		// اسم السلاح
		const weaponName = weapon.replace('WEAPON_', '').toLowerCase();
		ctx.fillText(weaponName, x + barWidth / 2, canvas.height - 10);

		// العدد
		ctx.fillText(count.toString(), x + barWidth / 2, y - 5);
	});
}

function exportStats() {
	const statsData = {
		...playerStats,
		exportDate: new Date().toISOString(),
		sessionDuration: Date.now() - playerStats.sessionStart
	};

	const dataStr = JSON.stringify(statsData, null, 2);
	const dataBlob = new Blob([dataStr], {type: 'application/json'});

	const link = document.createElement('a');
	link.href = URL.createObjectURL(dataBlob);
	link.download = `killfeed-stats-${new Date().toISOString().split('T')[0]}.json`;
	link.click();

	alert('تم تصدير الإحصائيات بنجاح!');
}

function resetStats() {
	if (confirm('هل أنت متأكد من إعادة تعيين جميع الإحصائيات؟ لا يمكن التراجع عن هذا الإجراء.')) {
		playerStats = {
			totalKills: 0,
			totalDeaths: 0,
			headshots: 0,
			bestStreak: 0,
			currentStreak: 0,
			longestKill: 0,
			weaponStats: {},
			sessionStart: Date.now()
		};

		saveSettings();
		updateStatsDisplay();
		alert('تم إعادة تعيين جميع الإحصائيات');
	}
}

// وظائف التصفية
function toggleFilters(e) {
	filterSettings.enabled = e.target.checked;
	const filterControls = document.getElementById('filter-controls');

	if (filterSettings.enabled) {
		filterControls.classList.remove('disabled');
	} else {
		filterControls.classList.add('disabled');
	}

	saveSettings();
}

function updateMinDistance(e) {
	filterSettings.minDistance = parseInt(e.target.value);
	document.getElementById('min-distance-value').textContent = e.target.value + 'm';

	// التأكد من أن الحد الأدنى لا يتجاوز الحد الأقصى
	const maxDistance = document.getElementById('max-distance');
	if (filterSettings.minDistance > filterSettings.maxDistance) {
		maxDistance.value = filterSettings.minDistance;
		updateMaxDistance({ target: maxDistance });
	}
}

function updateMaxDistance(e) {
	filterSettings.maxDistance = parseInt(e.target.value);
	document.getElementById('max-distance-value').textContent = e.target.value + 'm';

	// التأكد من أن الحد الأقصى لا يقل عن الحد الأدنى
	const minDistance = document.getElementById('min-distance');
	if (filterSettings.maxDistance < filterSettings.minDistance) {
		minDistance.value = filterSettings.maxDistance;
		updateMinDistance({ target: minDistance });
	}
}

function applyFilters() {
	// جمع إعدادات التصفية
	const weaponFilter = document.getElementById('weapon-filter');
	filterSettings.weaponTypes = Array.from(weaponFilter.selectedOptions).map(option => option.value);

	filterSettings.hideMyKills = document.getElementById('hide-my-kills').checked;
	filterSettings.hideMyDeaths = document.getElementById('hide-my-deaths').checked;
	filterSettings.headshotsOnly = document.getElementById('headshots-only').checked;
	filterSettings.multikillOnly = document.getElementById('multikill-only').checked;

	saveSettings();
	alert('تم تطبيق إعدادات التصفية');
}

function resetFilters() {
	filterSettings = {
		enabled: false,
		weaponTypes: ['all'],
		minDistance: 0,
		maxDistance: 500,
		hideMyKills: false,
		hideMyDeaths: false,
		headshotsOnly: false,
		multikillOnly: false
	};

	// تحديث واجهة المستخدم
	document.getElementById('enable-filters').checked = false;
	document.getElementById('weapon-filter').selectedIndex = 0;
	document.getElementById('min-distance').value = 0;
	document.getElementById('max-distance').value = 500;
	document.getElementById('min-distance-value').textContent = '0m';
	document.getElementById('max-distance-value').textContent = '500m';
	document.getElementById('hide-my-kills').checked = false;
	document.getElementById('hide-my-deaths').checked = false;
	document.getElementById('headshots-only').checked = false;
	document.getElementById('multikill-only').checked = false;

	document.getElementById('filter-controls').classList.add('disabled');

	saveSettings();
	alert('تم إعادة تعيين إعدادات التصفية');
}

// فحص ما إذا كان القتل يجب عرضه حسب التصفية
function shouldShowKill(killer, victim, headshot, design, weapon, distance) {
	if (!filterSettings.enabled) return true;

	// فحص إخفاء قتلاتي/وفياتي
	if (filterSettings.hideMyKills && design === 'teal-design') return false;
	if (filterSettings.hideMyDeaths && design === 'red-design') return false;

	// فحص الهيدشوت فقط
	if (filterSettings.headshotsOnly && !headshot) return false;

	// فحص نوع السلاح
	if (!filterSettings.weaponTypes.includes('all')) {
		const weaponType = getWeaponType(weapon);
		if (!filterSettings.weaponTypes.includes(weaponType)) return false;
	}

	// فحص المسافة
	if (distance) {
		const distanceNum = parseInt(distance.replace('m', ''));
		if (distanceNum < filterSettings.minDistance || distanceNum > filterSettings.maxDistance) {
			return false;
		}
	}

	return true;
}

// تحديد نوع السلاح
function getWeaponType(weapon) {
	if (!weapon) return 'unknown';

	const weaponName = weapon.toLowerCase();

	if (weaponName.includes('pistol') || weaponName.includes('revolver')) return 'pistol';
	if (weaponName.includes('rifle') || weaponName.includes('carbine')) return 'rifle';
	if (weaponName.includes('sniper') || weaponName.includes('marksman')) return 'sniper';
	if (weaponName.includes('shotgun')) return 'shotgun';
	if (weaponName.includes('smg') || weaponName.includes('micro')) return 'smg';
	if (weaponName.includes('grenade') || weaponName.includes('explosive') || weaponName.includes('rpg')) return 'explosive';
	if (weaponName.includes('knife') || weaponName.includes('bat') || weaponName.includes('crowbar')) return 'melee';

	return 'other';
}

// وظائف الإشعارات
function toggleNotifications(e) {
	notificationSettings.enabled = e.target.checked;
	const notificationControls = document.getElementById('notification-controls');

	if (notificationSettings.enabled) {
		notificationControls.classList.remove('disabled');
	} else {
		notificationControls.classList.add('disabled');
	}

	saveSettings();
}

function updateNotificationDuration(e) {
	notificationSettings.duration = parseFloat(e.target.value) * 1000;
	document.getElementById('notification-duration-value').textContent = e.target.value + 's';
	saveSettings();
}

function updateNotificationPosition(e) {
	notificationSettings.position = e.target.value;
	const container = document.getElementById('notifications-container');

	// إزالة جميع الفئات السابقة
	container.className = '';

	// إضافة الفئة الجديدة
	if (e.target.value !== 'top-right') {
		container.classList.add(e.target.value);
	}

	saveSettings();
}

function testNotification() {
	showNotification('🎯', 'إشعار تجريبي', 'هذا مثال على الإشعارات الذكية!', 'achievement');
}

function resetNotifications() {
	notificationSettings = {
		enabled: true,
		killstreakNotifications: true,
		headshotStreakNotifications: true,
		longshotNotifications: true,
		revengeNotifications: true,
		multikillNotifications: true,
		achievementNotifications: true,
		duration: 4000,
		position: 'top-right'
	};

	// تحديث واجهة المستخدم
	document.getElementById('enable-notifications').checked = true;
	document.getElementById('notification-duration').value = '4';
	document.getElementById('notification-duration-value').textContent = '4s';
	document.getElementById('notification-position').value = 'top-right';
	document.getElementById('notification-controls').classList.remove('disabled');

	// إعادة تعيين موقع الحاوية
	const container = document.getElementById('notifications-container');
	container.className = '';

	saveSettings();
	alert('تم إعادة تعيين إعدادات الإشعارات');
}

function showNotification(icon, title, message, type = 'default') {
	if (!notificationSettings.enabled) return;

	const container = document.getElementById('notifications-container');
	const notification = document.createElement('div');
	notification.className = `notification ${type}`;

	notification.innerHTML = `
		<button class="notification-close">×</button>
		<div class="notification-header">
			<span class="notification-icon">${icon}</span>
			<span class="notification-title">${title}</span>
		</div>
		<p class="notification-message">${message}</p>
	`;

	// إضافة مستمع إغلاق
	notification.querySelector('.notification-close').addEventListener('click', () => {
		notification.remove();
	});

	container.appendChild(notification);

	// إزالة تلقائية بعد المدة المحددة
	setTimeout(() => {
		if (notification.parentNode) {
			notification.style.animation = 'slideInNotification 0.3s ease-out reverse';
			setTimeout(() => {
				notification.remove();
			}, 300);
		}
	}, notificationSettings.duration);
}

// معالجة إشعارات القتل
function handleKillNotifications(killer, victim, headshot, design, weapon, distance) {
	if (!notificationSettings.enabled) return;

	const currentTime = Date.now();

	if (design === 'teal-design') {
		// أنت قتلت شخصاً

		// إشعار القتل المتتالي
		if (notificationSettings.killstreakNotifications && killStreak >= 3) {
			const streakMessages = {
				3: 'قتل ثلاثي!',
				5: 'قتل خماسي!',
				7: 'قتل سباعي!',
				10: 'قتل عشاري! مذهل!'
			};

			if (streakMessages[killStreak]) {
				showNotification('🔥', 'قتل متتالي!', streakMessages[killStreak], 'killstreak');
			}
		}

		// إشعار الهيدشوت المتتالي
		if (notificationSettings.headshotStreakNotifications && headshot) {
			headshotStreak++;
			if (headshotStreak >= 3) {
				showNotification('🎯', 'هيدشوت متتالي!', `${headshotStreak} هيدشوت متتالي!`, 'achievement');
			}
		} else {
			headshotStreak = 0;
		}

		// إشعار القتل من مسافة بعيدة
		if (notificationSettings.longshotNotifications && distance) {
			const distanceNum = parseInt(distance.replace('m', ''));
			if (distanceNum >= 100) {
				showNotification('📏', 'قتل من مسافة بعيدة!', `قتل من ${distance}!`, 'longshot');
			}
		}

		// إشعار القتل المتعدد السريع
		if (notificationSettings.multikillNotifications && currentTime - lastKillTime < 3000) {
			showNotification('⚡', 'قتل سريع!', 'قتل متعدد في وقت قصير!', 'achievement');
		}

		// إشعار الانتقام
		if (notificationSettings.revengeNotifications && victim.name) {
			if (recentKillers[victim.name] && currentTime - recentKillers[victim.name] < 30000) {
				showNotification('⚔️', 'انتقام!', `انتقمت من ${victim.name}!`, 'revenge');
				delete recentKillers[victim.name];
			}
		}

		lastKillTime = currentTime;

	} else if (design === 'red-design') {
		// أنت مت
		headshotStreak = 0;

		// تسجيل القاتل للانتقام المحتمل
		if (killer.name) {
			recentKillers[killer.name] = currentTime;
		}
	}
}
